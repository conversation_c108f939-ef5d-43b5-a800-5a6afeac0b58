import React, { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { validateEmailDomain } from '@/lib/email-validator';
import { ArrowLeft, ArrowRight, CircleCheck, AlertCircle, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { useErrorDialog } from '@/hooks/use-error-dialog';
import { useConfirmationDialog } from '@/hooks/use-confirmation-dialog';

// Validation schema
const createFormSchema = (requireAllowedEmail: boolean) => {
  return z.object({
    fullName: z.string().min(2, "Full name is required"),
    email: z.string()
      .email("Please enter a valid email address")
      .refine(
        (email) => validateEmailDomain(email).isValid,
        (email) => ({
          message: validateEmailDomain(email).message || "Email domain not allowed"
        })
      ),
    country: z.string().min(1, "Country is required"),
    appType: z.string().optional(),
    macAddress: z.string().optional(),
  });
};

type FormValues = {
  fullName: string;
  email: string;
  country: string;
  appType?: string;
  macAddress?: string;
};

export default function CustomCheckoutPage() {
  const [, params] = useRoute('/checkout/:slug');
  const slug = params?.slug;
  const [checkoutState, setCheckoutState] = useState<{
    status: 'loading' | 'form' | 'processing' | 'success' | 'error' | 'not-found' | 'expired';
    paypalInvoiceUrl?: string;
    paypalButtonHtml?: string;
    sellpassButtonHtml?: string;
    sellpassHeadScripts?: string;
    sellpassFooterScripts?: string;
    error?: string;
    debug?: {
      scriptsLoaded: boolean;
      buttonRendered: boolean;
    };
  }>({ status: 'loading' });

  const [formData, setFormData] = useState({
    email: '',
    name: '',
    appType: '',
    macAddress: ''
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const { toast } = useToast();

  // Query to fetch the custom checkout page
  const { data: page, isLoading, error } = useQuery({
    queryKey: [`/api/custom-checkout/public/${slug}`],
    queryFn: () => apiRequest(`/api/custom-checkout/public/${slug}`, 'GET'),
    enabled: !!slug,
    retry: false,
  });

  // Handle query state changes
  React.useEffect(() => {
    if (error) {
      console.log('Query error:', error);
      if (error.message?.includes('404')) {
        if (error.message?.includes('expired')) {
          setCheckoutState({ status: 'expired' });
        } else {
          setCheckoutState({ status: 'not-found' });
        }
      } else {
        setCheckoutState({
          status: 'error',
          error: error.message || 'Failed to load checkout page'
        });
      }
    } else if (page && !isLoading) {
      console.log('Query successful, received data:', page);
      setCheckoutState({ status: 'form' });
    }
  }, [page, isLoading, error]);

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.name.trim()) {
      errors.name = 'Full name is required';
    }

    if (page?.requiresAppType && !formData.appType) {
      errors.appType = 'Please select an app type';
    }

    if (page?.requiresMacAddress && !formData.macAddress.trim()) {
      errors.macAddress = 'MAC address is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      });
      return;
    }

    setCheckoutState({ status: 'processing' });

    try {
      const response = await apiRequest(`/api/custom-checkout/checkout/${slug}`, 'POST', {
        fullName: formData.name,
        email: formData.email,
        country: 'United States', // Default country for now
        appType: formData.appType || undefined,
        macAddress: formData.macAddress || undefined,
      });

      if (response.paypalButtonHtml) {
        setCheckoutState({
          status: 'success',
          paypalButtonHtml: response.paypalButtonHtml,
        });
      } else if (response.paypalInvoiceUrl) {
        setCheckoutState({
          status: 'success',
          paypalInvoiceUrl: response.paypalInvoiceUrl,
        });
      } else {
        setCheckoutState({
          status: 'success',
        });
      }

      toast({
        title: "Order Created",
        description: "Your order has been created successfully!",
      });
    } catch (error: any) {
      console.error('Checkout submission error:', error);
      setCheckoutState({
        status: 'error',
        error: error.message || 'Failed to process checkout',
      });
      toast({
        title: "Error",
        description: error.message || "Failed to process checkout",
        variant: "destructive",
      });
    }
  };

  // Simple render for now
  if (!slug) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">No Slug Found</h1>
          <p>The checkout page slug is missing from the URL.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <p>Loading checkout page for: {slug}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Error</h1>
          <p>Failed to load checkout page: {error.message}</p>
        </div>
      </div>
    );
  }

  if (!page) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Page Not Found</h1>
          <p>The checkout page could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="bg-white border-b py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <a href="/" className="text-xl font-bold">
            PayPal Invoicer
          </a>
          <div className="text-sm text-muted-foreground">
            Secure Checkout
          </div>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-8 md:py-12">
        <div className="max-w-2xl mx-auto">
          <Card className="shadow-md">
            <CardContent className="p-6 md:p-8">
              <div className="text-center mb-8">
                <h1 className="text-2xl md:text-3xl font-bold mb-2">{page.title}</h1>
                <p className="text-muted-foreground">
                  {page.isTrial ? 'Start your trial today' : 'Complete your purchase'}
                </p>
              </div>

              {checkoutState.status === 'form' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Customer Information</h3>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="email">Email Address *</Label>
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={formData.email}
                            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                            className={formErrors.email ? 'border-red-500' : ''}
                            required
                          />
                          {formErrors.email && (
                            <p className="text-sm text-red-500 mt-1">{formErrors.email}</p>
                          )}
                        </div>
                        <div>
                          <Label htmlFor="name">Full Name *</Label>
                          <Input
                            id="name"
                            type="text"
                            placeholder="John Doe"
                            value={formData.name}
                            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                            className={formErrors.name ? 'border-red-500' : ''}
                            required
                          />
                          {formErrors.name && (
                            <p className="text-sm text-red-500 mt-1">{formErrors.name}</p>
                          )}
                        </div>

                        {page?.requiresAppType && (
                          <div>
                            <Label htmlFor="appType">App Type *</Label>
                            <select
                              id="appType"
                              value={formData.appType}
                              onChange={(e) => setFormData({ ...formData, appType: e.target.value })}
                              className={`w-full px-3 py-2 border rounded-md ${formErrors.appType ? 'border-red-500' : 'border-gray-300'}`}
                              required
                            >
                              <option value="">Select App Type</option>
                              <option value="IPTV Smarters Pro">IPTV Smarters Pro</option>
                              <option value="MAG">MAG</option>
                              <option value="Other">Other</option>
                            </select>
                            {formErrors.appType && (
                              <p className="text-sm text-red-500 mt-1">{formErrors.appType}</p>
                            )}
                          </div>
                        )}

                        {page?.requiresMacAddress && (
                          <div>
                            <Label htmlFor="macAddress">MAC Address *</Label>
                            <Input
                              id="macAddress"
                              type="text"
                              placeholder="00:1A:2B:3C:4D:5E"
                              value={formData.macAddress}
                              onChange={(e) => setFormData({ ...formData, macAddress: e.target.value })}
                              className={formErrors.macAddress ? 'border-red-500' : ''}
                              required
                            />
                            {formErrors.macAddress && (
                              <p className="text-sm text-red-500 mt-1">{formErrors.macAddress}</p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-4">Order Summary</h3>
                      <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                        <div className="flex justify-between">
                          <span>Product:</span>
                          <span className="font-medium">{page.productName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Price:</span>
                          <span className="font-medium">${page.price}</span>
                        </div>
                        {page.isTrial && (
                          <div className="text-sm text-blue-600 mt-2">
                            This is a trial subscription
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={handleSubmit}
                      disabled={checkoutState.status === 'processing'}
                    >
                      {checkoutState.status === 'processing' ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Processing...
                        </>
                      ) : (
                        `${page.isTrial ? 'Start Trial' : 'Complete Purchase'} - $${page.price}`
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {checkoutState.status === 'processing' && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <h3 className="text-lg font-semibold mb-2">Processing...</h3>
                  <p className="text-muted-foreground">Please wait while we process your order.</p>
                </div>
              )}

              {checkoutState.status === 'success' && (
                <div className="text-center py-8">
                  <div className="mb-6">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Order Created Successfully!</h3>
                    <p className="text-muted-foreground mb-6">
                      Your order has been created. Please complete the payment below.
                    </p>
                  </div>

                  {checkoutState.paypalButtonHtml && (
                    <div
                      className="payment-button-container"
                      dangerouslySetInnerHTML={{ __html: checkoutState.paypalButtonHtml }}
                    />
                  )}

                  {checkoutState.paypalInvoiceUrl && (
                    <div className="space-y-4">
                      <Button
                        className="w-full"
                        size="lg"
                        onClick={() => window.open(checkoutState.paypalInvoiceUrl, '_blank')}
                      >
                        Complete Payment with PayPal
                      </Button>
                      <p className="text-sm text-muted-foreground">
                        You will be redirected to PayPal to complete your payment.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {checkoutState.status === 'error' && (
                <div className="text-center py-8">
                  <div className="mb-6">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Something went wrong</h3>
                    <p className="text-muted-foreground mb-6">
                      {checkoutState.error || 'An unexpected error occurred. Please try again.'}
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => setCheckoutState({ status: 'form' })}
                    >
                      Try Again
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>

      <footer className="bg-white border-t py-6">
        <div className="container mx-auto px-4 text-center text-sm text-muted-foreground">
          <p>© {new Date().getFullYear()} PayPal Invoicer. All rights reserved.</p>
          <p className="mt-1">Secure payment processing provided by PayPal.</p>
        </div>
      </footer>
    </div>
  );
}
  const [, params] = useRoute('/checkout/:slug');
  const slug = params?.slug;
  const [checkoutState, setCheckoutState] = useState<{
    status: 'loading' | 'form' | 'processing' | 'success' | 'error' | 'not-found' | 'expired';
    paypalInvoiceUrl?: string;
    paypalButtonHtml?: string;
    sellpassButtonHtml?: string;
    sellpassHeadScripts?: string;
    sellpassFooterScripts?: string;
    error?: string;
    debug?: {
      scriptsLoaded: boolean;
      buttonRendered: boolean;
    };
  }>({ status: 'loading' });

  const { toast } = useToast();

  // Debug logging
  console.log('CustomCheckoutPage rendered with slug:', slug);
  console.log('Current checkout state:', checkoutState);

  // Query to fetch the custom checkout page
  const { data: page, isLoading, error } = useQuery({
    queryKey: [`/api/custom-checkout/public/${slug}`],
    queryFn: () => {
      console.log('Query function called for slug:', slug);
      return apiRequest(`/api/custom-checkout/public/${slug}`, 'GET');
    },
    enabled: !!slug,
    retry: false,
  });

  console.log('Query state - isLoading:', isLoading, 'error:', error, 'page:', page);

  // Handle query state changes
  React.useEffect(() => {
    if (error) {
      console.log('Query error:', error);
      if (error.message?.includes('404')) {
        if (error.message?.includes('expired')) {
          setCheckoutState({ status: 'expired' });
        } else {
          setCheckoutState({ status: 'not-found' });
        }
      } else {
        setCheckoutState({
          status: 'error',
          error: error.message || 'Failed to load checkout page'
        });
      }
    } else if (page && !isLoading) {
      console.log('Query successful, received data:', page);
      setCheckoutState({ status: 'form' });
    }
  }, [page, isLoading, error]);

  // Form setup
  const form = useForm<FormValues>({
    resolver: zodResolver(createFormSchema(!!page?.requireAllowedEmail)),
    defaultValues: {
      fullName: '',
      email: '',
      country: '',
      appType: '',
      macAddress: '',
    },
    mode: 'onChange',
  });

  // Checkout mutation
  const { mutate, isPending } = useMutation({
    mutationFn: async (data: FormValues) => {
      return apiRequest(`/api/custom-checkout/checkout/${slug}`, 'POST', data);
    },
    onSuccess: (data) => {
      console.log('Checkout success data:', data);
      setCheckoutState({
        status: 'success',
        paypalInvoiceUrl: data.paypalInvoiceUrl,
        paypalButtonHtml: data.paypalButtonHtml,
        sellpassButtonHtml: data.sellpassButtonHtml,
        sellpassHeadScripts: data.sellpassHeadScripts,
        sellpassFooterScripts: data.sellpassFooterScripts,
        debug: {
          scriptsLoaded: false,
          buttonRendered: false
        }
      });
    },
    onError: (error: any) => {
      // Check if it's an email validation error
      if (error.message && error.message.includes('email')) {
        // Show a more detailed error dialog with guidance for new users
        showError(
          "Email Not Found",
          <div className="space-y-2">
            <p>The email you entered is not in our database.</p>
            <p>This checkout page is only for existing subscribers. If you are not an existing subscriber, please order a test plan first.</p>
            <p>Contact support if you believe this is an error.</p>
          </div>
        );
        // Reset form status to allow resubmission
        setCheckoutState({ status: 'form' });
      } else {
        // Handle other errors with error dialog
        showError(
          "Checkout Error",
          <p>{error.message || 'Failed to process checkout'}</p>
        );
        setCheckoutState({
          status: 'error',
          error: error.message || 'Failed to process checkout'
        });
      }
    }
  });

  const { showConfirmation } = useConfirmationDialog();
  const { showError } = useErrorDialog();

  // Submit handler
  const onSubmit = (data: FormValues) => {
    // Custom validation for MAC address
    const requiresMac = ['MAG', 'Formuler Z', 'Smart STB', 'STBEMU'].includes(data.appType || '');
    let hasError = false;

    if (requiresMac && !data.macAddress) {
      form.setError('macAddress', {
        type: 'manual',
        message: 'MAC address is required for this device type'
      });
      hasError = true;
    } else if (data.macAddress) {
      const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
      if (!macRegex.test(data.macAddress)) {
        form.setError('macAddress', {
          type: 'manual',
          message: 'Please enter a valid MAC address in the format 00:1A:72:c9:dc:a4'
        });
        hasError = true;
      }
    }

    if (hasError) {
      return;
    }

    // Prepare the data for submission
    const formData = {
      ...data,
      // Only include macAddress if it's required or provided
      macAddress: requiresMac ? data.macAddress : undefined
    };

    // Show confirmation dialog before proceeding with purchase
    showConfirmation(
      "Confirm Your Purchase",
      <div className="space-y-3">
        {page?.confirmationMessage ? (
          <div dangerouslySetInnerHTML={{ __html: page.confirmationMessage }} />
        ) : (
          <>
            <p>You are about to purchase:</p>
            <p className="font-medium">{page?.productName}</p>
            <p>Price: {formatCurrency(page?.price || 0)}</p>
            <p className="text-sm text-muted-foreground mt-2">
              By clicking confirm, you agree to our terms and conditions.
            </p>
          </>
        )}
      </div>,
      () => {
        // This function runs when the user confirms
        setCheckoutState({ status: 'processing' });
        mutate(formData);
      },
      {
        confirmText: "Proceed with Purchase",
        cancelText: "Cancel"
      }
    );
  };

  // Handler to try again after error
  const handleTryAgain = () => {
    setCheckoutState({ status: 'form' });
  };



  // Add head scripts for Sellpass buttons if needed
  useEffect(() => {
    if (checkoutState.status === 'success' && checkoutState.sellpassHeadScripts) {
      // Create a script element
      const headScriptElement = document.createElement('div');
      headScriptElement.id = 'sellpass-head-scripts';
      headScriptElement.innerHTML = checkoutState.sellpassHeadScripts;
      document.head.appendChild(headScriptElement);

      // Cleanup function to remove the script when component unmounts
      return () => {
        const element = document.getElementById('sellpass-head-scripts');
        if (element) {
          document.head.removeChild(element);
        }
      };
    }
  }, [checkoutState.status, checkoutState.sellpassHeadScripts]);

  // Add footer scripts for Sellpass buttons if needed
  useEffect(() => {
    if (checkoutState.status === 'success' && checkoutState.sellpassFooterScripts) {
      // Create a script element
      const footerScriptElement = document.createElement('div');
      footerScriptElement.id = 'sellpass-footer-scripts';
      footerScriptElement.innerHTML = checkoutState.sellpassFooterScripts;
      document.body.appendChild(footerScriptElement);

      // Cleanup function to remove the script when component unmounts
      return () => {
        const element = document.getElementById('sellpass-footer-scripts');
        if (element) {
          document.body.removeChild(element);
        }
      };
    }
  }, [checkoutState.status, checkoutState.sellpassFooterScripts]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Render checkout form or status screens
  const renderContent = () => {
    if (checkoutState.status === 'loading' || isLoading) {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6">
            <div className="animate-spin h-12 w-12 border-2 border-primary border-t-transparent rounded-full"></div>
          </div>
          <h3 className="text-xl font-semibold mb-2">Loading Checkout Page</h3>
          <p className="text-center text-muted-foreground">Please wait a moment...</p>
        </div>
      );
    } else if (checkoutState.status === 'not-found') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-destructive">
            <AlertCircle className="h-16 w-16" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Checkout Page Not Found</h3>
          <p className="text-center text-muted-foreground mb-6">
            The checkout page you're looking for doesn't exist or has been removed.
          </p>
        </div>
      );
    } else if (checkoutState.status === 'expired') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-amber-500">
            <AlertTriangle className="h-16 w-16" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Checkout Page Expired</h3>
          <p className="text-center text-muted-foreground mb-6">
            This checkout page has expired and is no longer available.
          </p>
        </div>
      );
    } else if (checkoutState.status === 'form' && page) {
      return (
        <div>
          <div className="mb-6">
            <div className="flex justify-between items-start">
              <h2 className="text-2xl font-bold mb-2">{page.productName}</h2>
              {page.isTrialCheckout && (
                <div className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm font-medium">
                  Trial Subscription
                </div>
              )}
            </div>
            <div
              className="text-muted-foreground"
              dangerouslySetInnerHTML={{ __html: page.productDescription }}
            />

            {page.imageUrl && (
              <div className="mt-4 rounded-md overflow-hidden">
                <img
                  src={page.imageUrl}
                  alt={page.productName}
                  className="w-full h-auto object-cover"
                />
              </div>
            )}
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-4">Customer Information</h3>
                <div className="grid grid-cols-1 gap-4">
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="John Smith" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <div className="flex items-center mt-1 text-xs text-amber-600">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          <span>Only Gmail, Hotmail, Yahoo, and other common email providers are accepted</span>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {page?.requireAllowedEmail && (
                    <div className="flex items-start mt-1 text-xs text-amber-600 mb-4">
                      <AlertTriangle className="h-3 w-3 mr-1 mt-0.5 flex-shrink-0" />
                      <span>
                        This purchase is only available to existing subscribers.
                        Please enter the email address associated with your subscription.
                        New users should order a test plan first.
                      </span>
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country <span className="text-red-500">*</span></FormLabel>
                        <FormControl>
                          <select
                            {...field}
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          >
                            <option value="" disabled>Select your country</option>
                            <option value="Afghanistan">Afghanistan</option>
                            <option value="Albania">Albania</option>
                            <option value="Algeria">Algeria</option>
                            <option value="Andorra">Andorra</option>
                            <option value="Angola">Angola</option>
                            <option value="Antigua and Barbuda">Antigua and Barbuda</option>
                            <option value="Argentina">Argentina</option>
                            <option value="Armenia">Armenia</option>
                            <option value="Australia">Australia</option>
                            <option value="Austria">Austria</option>
                            <option value="Azerbaijan">Azerbaijan</option>
                            <option value="Bahamas">Bahamas</option>
                            <option value="Bahrain">Bahrain</option>
                            <option value="Bangladesh">Bangladesh</option>
                            <option value="Barbados">Barbados</option>
                            <option value="Belarus">Belarus</option>
                            <option value="Belgium">Belgium</option>
                            <option value="Belize">Belize</option>
                            <option value="Benin">Benin</option>
                            <option value="Bhutan">Bhutan</option>
                            <option value="Bolivia">Bolivia</option>
                            <option value="Bosnia and Herzegovina">Bosnia and Herzegovina</option>
                            <option value="Botswana">Botswana</option>
                            <option value="Brazil">Brazil</option>
                            <option value="Brunei">Brunei</option>
                            <option value="Bulgaria">Bulgaria</option>
                            <option value="Burkina Faso">Burkina Faso</option>
                            <option value="Burundi">Burundi</option>
                            <option value="Cabo Verde">Cabo Verde</option>
                            <option value="Cambodia">Cambodia</option>
                            <option value="Cameroon">Cameroon</option>
                            <option value="Canada">Canada</option>
                            <option value="Central African Republic">Central African Republic</option>
                            <option value="Chad">Chad</option>
                            <option value="Chile">Chile</option>
                            <option value="China">China</option>
                            <option value="Colombia">Colombia</option>
                            <option value="Comoros">Comoros</option>
                            <option value="Congo">Congo</option>
                            <option value="Costa Rica">Costa Rica</option>
                            <option value="Croatia">Croatia</option>
                            <option value="Cuba">Cuba</option>
                            <option value="Cyprus">Cyprus</option>
                            <option value="Czech Republic">Czech Republic</option>
                            <option value="Denmark">Denmark</option>
                            <option value="Djibouti">Djibouti</option>
                            <option value="Dominica">Dominica</option>
                            <option value="Dominican Republic">Dominican Republic</option>
                            <option value="Ecuador">Ecuador</option>
                            <option value="Egypt">Egypt</option>
                            <option value="El Salvador">El Salvador</option>
                            <option value="Equatorial Guinea">Equatorial Guinea</option>
                            <option value="Eritrea">Eritrea</option>
                            <option value="Estonia">Estonia</option>
                            <option value="Eswatini">Eswatini</option>
                            <option value="Ethiopia">Ethiopia</option>
                            <option value="Fiji">Fiji</option>
                            <option value="Finland">Finland</option>
                            <option value="France">France</option>
                            <option value="Gabon">Gabon</option>
                            <option value="Gambia">Gambia</option>
                            <option value="Georgia">Georgia</option>
                            <option value="Germany">Germany</option>
                            <option value="Ghana">Ghana</option>
                            <option value="Greece">Greece</option>
                            <option value="Grenada">Grenada</option>
                            <option value="Guatemala">Guatemala</option>
                            <option value="Guinea">Guinea</option>
                            <option value="Guinea-Bissau">Guinea-Bissau</option>
                            <option value="Guyana">Guyana</option>
                            <option value="Haiti">Haiti</option>
                            <option value="Honduras">Honduras</option>
                            <option value="Hungary">Hungary</option>
                            <option value="Iceland">Iceland</option>
                            <option value="India">India</option>
                            <option value="Indonesia">Indonesia</option>
                            <option value="Iran">Iran</option>
                            <option value="Iraq">Iraq</option>
                            <option value="Ireland">Ireland</option>
                            <option value="Israel">Israel</option>
                            <option value="Italy">Italy</option>
                            <option value="Jamaica">Jamaica</option>
                            <option value="Japan">Japan</option>
                            <option value="Jordan">Jordan</option>
                            <option value="Kazakhstan">Kazakhstan</option>
                            <option value="Kenya">Kenya</option>
                            <option value="Kiribati">Kiribati</option>
                            <option value="Korea, North">Korea, North</option>
                            <option value="Korea, South">Korea, South</option>
                            <option value="Kosovo">Kosovo</option>
                            <option value="Kuwait">Kuwait</option>
                            <option value="Kyrgyzstan">Kyrgyzstan</option>
                            <option value="Laos">Laos</option>
                            <option value="Latvia">Latvia</option>
                            <option value="Lebanon">Lebanon</option>
                            <option value="Lesotho">Lesotho</option>
                            <option value="Liberia">Liberia</option>
                            <option value="Libya">Libya</option>
                            <option value="Liechtenstein">Liechtenstein</option>
                            <option value="Lithuania">Lithuania</option>
                            <option value="Luxembourg">Luxembourg</option>
                            <option value="Madagascar">Madagascar</option>
                            <option value="Malawi">Malawi</option>
                            <option value="Malaysia">Malaysia</option>
                            <option value="Maldives">Maldives</option>
                            <option value="Mali">Mali</option>
                            <option value="Malta">Malta</option>
                            <option value="Marshall Islands">Marshall Islands</option>
                            <option value="Mauritania">Mauritania</option>
                            <option value="Mauritius">Mauritius</option>
                            <option value="Mexico">Mexico</option>
                            <option value="Micronesia">Micronesia</option>
                            <option value="Moldova">Moldova</option>
                            <option value="Monaco">Monaco</option>
                            <option value="Mongolia">Mongolia</option>
                            <option value="Montenegro">Montenegro</option>
                            <option value="Morocco">Morocco</option>
                            <option value="Mozambique">Mozambique</option>
                            <option value="Myanmar">Myanmar</option>
                            <option value="Namibia">Namibia</option>
                            <option value="Nauru">Nauru</option>
                            <option value="Nepal">Nepal</option>
                            <option value="Netherlands">Netherlands</option>
                            <option value="New Zealand">New Zealand</option>
                            <option value="Nicaragua">Nicaragua</option>
                            <option value="Niger">Niger</option>
                            <option value="Nigeria">Nigeria</option>
                            <option value="North Macedonia">North Macedonia</option>
                            <option value="Norway">Norway</option>
                            <option value="Oman">Oman</option>
                            <option value="Pakistan">Pakistan</option>
                            <option value="Palau">Palau</option>
                            <option value="Palestine">Palestine</option>
                            <option value="Panama">Panama</option>
                            <option value="Papua New Guinea">Papua New Guinea</option>
                            <option value="Paraguay">Paraguay</option>
                            <option value="Peru">Peru</option>
                            <option value="Philippines">Philippines</option>
                            <option value="Poland">Poland</option>
                            <option value="Portugal">Portugal</option>
                            <option value="Qatar">Qatar</option>
                            <option value="Romania">Romania</option>
                            <option value="Russia">Russia</option>
                            <option value="Rwanda">Rwanda</option>
                            <option value="Saint Kitts and Nevis">Saint Kitts and Nevis</option>
                            <option value="Saint Lucia">Saint Lucia</option>
                            <option value="Saint Vincent and the Grenadines">Saint Vincent and the Grenadines</option>
                            <option value="Samoa">Samoa</option>
                            <option value="San Marino">San Marino</option>
                            <option value="Sao Tome and Principe">Sao Tome and Principe</option>
                            <option value="Saudi Arabia">Saudi Arabia</option>
                            <option value="Senegal">Senegal</option>
                            <option value="Serbia">Serbia</option>
                            <option value="Seychelles">Seychelles</option>
                            <option value="Sierra Leone">Sierra Leone</option>
                            <option value="Singapore">Singapore</option>
                            <option value="Slovakia">Slovakia</option>
                            <option value="Slovenia">Slovenia</option>
                            <option value="Solomon Islands">Solomon Islands</option>
                            <option value="Somalia">Somalia</option>
                            <option value="South Africa">South Africa</option>
                            <option value="South Sudan">South Sudan</option>
                            <option value="Spain">Spain</option>
                            <option value="Sri Lanka">Sri Lanka</option>
                            <option value="Sudan">Sudan</option>
                            <option value="Suriname">Suriname</option>
                            <option value="Sweden">Sweden</option>
                            <option value="Switzerland">Switzerland</option>
                            <option value="Syria">Syria</option>
                            <option value="Taiwan">Taiwan</option>
                            <option value="Tajikistan">Tajikistan</option>
                            <option value="Tanzania">Tanzania</option>
                            <option value="Thailand">Thailand</option>
                            <option value="Timor-Leste">Timor-Leste</option>
                            <option value="Togo">Togo</option>
                            <option value="Tonga">Tonga</option>
                            <option value="Trinidad and Tobago">Trinidad and Tobago</option>
                            <option value="Tunisia">Tunisia</option>
                            <option value="Turkey">Turkey</option>
                            <option value="Turkmenistan">Turkmenistan</option>
                            <option value="Tuvalu">Tuvalu</option>
                            <option value="Uganda">Uganda</option>
                            <option value="Ukraine">Ukraine</option>
                            <option value="United Arab Emirates">United Arab Emirates</option>
                            <option value="United Kingdom">United Kingdom</option>
                            <option value="United States">United States</option>
                            <option value="Uruguay">Uruguay</option>
                            <option value="Uzbekistan">Uzbekistan</option>
                            <option value="Vanuatu">Vanuatu</option>
                            <option value="Vatican City">Vatican City</option>
                            <option value="Venezuela">Venezuela</option>
                            <option value="Vietnam">Vietnam</option>
                            <option value="Yemen">Yemen</option>
                            <option value="Zambia">Zambia</option>
                            <option value="Zimbabwe">Zimbabwe</option>
                          </select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Choose the application you are using:</label>
                    <select
                      value={form.watch('appType') || ''}
                      onChange={(e) => {
                        form.setValue('appType', e.target.value);
                        // Reset MAC address when changing app type
                        if (!['MAG', 'Formuler Z', 'Smart STB', 'STBEMU'].includes(e.target.value)) {
                          form.setValue('macAddress', '');
                        }
                      }}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="" disabled>Choose your application</option>
                      <option value="IPTV Smarters Pro">IPTV Smarters Pro</option>
                      <option value="GSE Smart IPTV">GSE Smart IPTV</option>
                      <option value="XCIPTV">XCIPTV</option>
                      <option value="Tivimate">Tivimate</option>
                      <option value="Royal IPTV">Royal IPTV</option>
                      <option value="Smart IPTV">Smart IPTV</option>
                      <option value="Set IPTV">Set IPTV</option>
                      <option value="Net IPTV">Net IPTV</option>
                      <option value="FLIX IPTV">FLIX IPTV</option>
                      <option value="SSIPTV">SSIPTV</option>
                      <option value="Duplex IPTV">Duplex IPTV</option>
                      <option value="iPlayTV">iPlayTV</option>
                      <option value="Mytvonline">Mytvonline</option>
                      <option value="IPTV Extreme">IPTV Extreme</option>
                      <option value="VLC Player">VLC Player</option>
                      <option value="IPTV Smaer Purple player">IPTV Smaer Purple player</option>
                      <option value="Perfect IPTV Player">Perfect IPTV Player</option>
                      <option value="M3U">M3U</option>
                      <option value="MAG">MAG</option>
                      <option value="Formuler Z">Formuler Z</option>
                      <option value="Smart STB">Smart STB</option>
                      <option value="STBEMU">STBEMU</option>
                    </select>
                  </div>

                  {['MAG', 'Formuler Z', 'Smart STB', 'STBEMU'].includes(form.watch('appType')) && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-1">Your Mac Address (required)</label>
                      <input
                        type="text"
                        placeholder="00:1A:72:c9:dc:a4"
                        value={form.watch('macAddress') || ''}
                        onChange={(e) => form.setValue('macAddress', e.target.value)}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                      />
                      <div className="text-xs text-muted-foreground mt-1">
                        Please enter the MAC address of your device, EX: 00:1A:72:c9:dc:a4.
                      </div>
                      {form.formState.errors.macAddress && (
                        <div className="text-xs text-red-500 mt-1">
                          {form.formState.errors.macAddress.message}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div className="border-t border-border pt-6 mb-6">
                <div className="flex justify-between mb-2">
                  <span>Subtotal</span>
                  <span>{formatCurrency(page.price)}</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>{formatCurrency(page.price)}</span>
                </div>
              </div>

              <div className="text-center">
                <Button
                  type="submit"
                  className="w-full bg-primary hover:bg-primary/90 text-primary-foreground py-6"
                  disabled={isPending}
                >
                  <span>Complete Purchase</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <p className="text-sm text-muted-foreground mt-2">
                  {page.paymentMethod === 'paypal'
                    ? "You'll receive a PayPal invoice via email to complete your purchase securely."
                    : "You'll receive a payment link via email to complete your purchase securely."}
                </p>
                {page?.requireUsername && (
                  <div className="text-sm text-amber-600 mt-2 flex items-start border-t border-amber-200 pt-2">
                    <AlertTriangle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium">Subscription Extension</p>
                      <p>This checkout is for existing subscribers only. If you're a new user, please order a test plan first before extending your subscription.</p>
                    </div>
                  </div>
                )}
              </div>
            </form>
          </Form>
        </div>
      );
    } else if (checkoutState.status === 'processing') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6">
            <div className="animate-spin h-12 w-12 border-2 border-primary border-t-transparent rounded-full"></div>
          </div>
          <h3 className="text-xl font-semibold mb-2">Processing Your Order</h3>
          <p className="text-center text-muted-foreground">We're processing your order. Please wait a moment...</p>
        </div>
      );
    } else if (checkoutState.status === 'success') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-[#27B376]">
            <CircleCheck className="h-16 w-16" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Order Successful!</h3>

          {/* For trial checkout pages, show the payment button directly */}
          {page?.isTrialCheckout ? (
            <>
              <p className="text-center text-muted-foreground mb-6">
                Please complete your payment using the button below.
              </p>
              {checkoutState.sellpassButtonHtml ? (
                <div className="mb-6">
                  {/* For Shoppy.gg buttons, we need to create the button element directly */}
                  {checkoutState.sellpassHeadScripts && checkoutState.sellpassHeadScripts.includes('shoppy.gg/api/embed.js') ? (
                    <>
                      <button
                        data-shoppy-product="6yi7rQp"
                        className="shoppy-button"
                        style={{
                          backgroundColor: '#3498db',
                          color: 'white',
                          padding: '10px 20px',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer',
                          fontSize: '16px',
                          fontWeight: 'bold'
                        }}
                      >
                        Pay Now
                      </button>

                      {/* Debug information */}
                      <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
                        <p><strong>Debug Info:</strong></p>
                        <p>Scripts loaded: {checkoutState.debug?.scriptsLoaded ? 'Yes' : 'No'}</p>
                        <p>Button HTML: {checkoutState.sellpassButtonHtml ? 'Present' : 'Missing'}</p>
                        <p>Head Scripts: {checkoutState.sellpassHeadScripts ? 'Present' : 'Missing'}</p>
                        <p>Product ID: 6yi7rQp</p>
                        <p className="text-amber-600 mt-2">Note: Payment buttons may not fully function on localhost</p>
                      </div>

                      {/* Preview button that opens Shoppy.gg directly */}
                      <div className="mt-4">
                        <p className="text-sm mb-2">If the button above doesn't work on localhost, use this direct link:</p>
                        <a
                          href="https://shoppy.gg/product/6yi7rQp"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-block bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                        >
                          Open Payment Page
                        </a>
                      </div>
                    </>
                  ) : (
                    <div dangerouslySetInnerHTML={{ __html: checkoutState.sellpassButtonHtml }} />
                  )}
                </div>
              ) : checkoutState.paypalButtonHtml ? (
                <div className="mb-6" dangerouslySetInnerHTML={{ __html: checkoutState.paypalButtonHtml }} />
              ) : checkoutState.paypalInvoiceUrl && (
                <div className="mb-6">
                  <Button
                    className="bg-primary hover:bg-primary/90 text-primary-foreground"
                    asChild
                  >
                    <a href={checkoutState.paypalInvoiceUrl} target="_blank" rel="noopener noreferrer">
                      Complete Payment
                    </a>
                  </Button>
                </div>
              )}
            </>
          ) : (
            // For regular checkout pages, only show message about email
            <p className="text-center text-muted-foreground mb-6">
              We've sent a payment link to your email address. Please check your inbox to complete your purchase.
            </p>
          )}
        </div>
      );
    } else if (checkoutState.status === 'error') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-destructive">
            <AlertCircle className="h-16 w-16" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Something went wrong</h3>
          <p className="text-center text-muted-foreground mb-6">
            {checkoutState.error || "We couldn't process your order. Please try again or contact support if the problem persists."}
          </p>
          <div className="text-center">
            <Button
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              onClick={handleTryAgain}
            >
              Try Again
            </Button>
          </div>
        </div>
      );
    }

    return null;
  };

  // Add Sellpass scripts to head
  useEffect(() => {
    if (checkoutState.sellpassHeadScripts) {
      // For Shoppy.gg specifically, we need to directly use the script URL
      if (checkoutState.sellpassHeadScripts.includes('shoppy.gg/api/embed.js')) {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = 'https://shoppy.gg/api/embed.js';
        script.id = 'shoppy-script';

        // Remove any existing script with the same ID
        const existingScript = document.getElementById('shoppy-script');
        if (existingScript) {
          existingScript.remove();
        }

        // Append to head
        document.head.appendChild(script);

        // Log for debugging
        console.log('Shoppy.gg script loaded');

        // Update debug state
        setCheckoutState(prevState => ({
          ...prevState,
          debug: {
            ...prevState.debug,
            scriptsLoaded: true
          }
        }));
      }
      // For other external scripts, extract the src
      else {
        // Extract the script src if it's an external script
        const scriptSrcMatch = checkoutState.sellpassHeadScripts.match(/src=["']([^"']+)["']/);

        if (scriptSrcMatch && scriptSrcMatch[1]) {
          // It's an external script, create a proper script element with src attribute
          const scriptSrc = scriptSrcMatch[1];
          const script = document.createElement('script');
          script.type = 'text/javascript';
          script.src = scriptSrc;
          script.id = 'sellpass-head-scripts';

          // Remove any existing script with the same ID
          const existingScript = document.getElementById('sellpass-head-scripts');
          if (existingScript) {
            existingScript.remove();
          }

          // Append to head
          document.head.appendChild(script);
        } else {
          // It's an inline script, use innerHTML
          const script = document.createElement('script');
          script.type = 'text/javascript';
          script.innerHTML = checkoutState.sellpassHeadScripts;
          script.id = 'sellpass-head-scripts';

          // Remove any existing script with the same ID
          const existingScript = document.getElementById('sellpass-head-scripts');
          if (existingScript) {
            existingScript.remove();
          }

          // Append to head
          document.head.appendChild(script);
        }
      }

      // Cleanup on unmount
      return () => {
        const scriptToRemove = document.getElementById('sellpass-head-scripts');
        if (scriptToRemove) {
          scriptToRemove.remove();
        }
      };
    }
  }, [checkoutState.sellpassHeadScripts]);

  // Add Sellpass footer scripts
  useEffect(() => {
    if (checkoutState.sellpassFooterScripts) {
      // For external scripts, we need to create a proper script element
      // Extract the script src if it's an external script
      const scriptSrcMatch = checkoutState.sellpassFooterScripts.match(/src=["']([^"']+)["']/);

      if (scriptSrcMatch && scriptSrcMatch[1]) {
        // It's an external script, create a proper script element with src attribute
        const scriptSrc = scriptSrcMatch[1];
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = scriptSrc;
        script.id = 'sellpass-footer-scripts';

        // Remove any existing script with the same ID
        const existingScript = document.getElementById('sellpass-footer-scripts');
        if (existingScript) {
          existingScript.remove();
        }

        // Append to body
        document.body.appendChild(script);
      } else {
        // It's an inline script, use innerHTML
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.innerHTML = checkoutState.sellpassFooterScripts;
        script.id = 'sellpass-footer-scripts';

        // Remove any existing script with the same ID
        const existingScript = document.getElementById('sellpass-footer-scripts');
        if (existingScript) {
          existingScript.remove();
        }

        // Append to body
        document.body.appendChild(script);
      }

      // Cleanup on unmount
      return () => {
        const scriptToRemove = document.getElementById('sellpass-footer-scripts');
        if (scriptToRemove) {
          scriptToRemove.remove();
        }
      };
    }
  }, [checkoutState.sellpassFooterScripts]);

  // Add a debug render to see if component is working
  if (!slug) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Debug: No Slug Found</h1>
          <p>The checkout page slug is missing from the URL.</p>
          <p>Current URL params: {JSON.stringify(params)}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="bg-white border-b py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <a href="/" className="text-xl font-bold">
            PayPal Invoicer
          </a>
          <div className="text-sm text-muted-foreground">
            Secure Checkout
          </div>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-8 md:py-12">
        <div className="max-w-2xl mx-auto">
          {/* Debug info */}
          <div className="mb-4 p-4 bg-yellow-100 border border-yellow-300 rounded">
            <h3 className="font-bold">Debug Info:</h3>
            <p>Slug: {slug}</p>
            <p>Is Loading: {isLoading ? 'Yes' : 'No'}</p>
            <p>Has Error: {error ? 'Yes' : 'No'}</p>
            <p>Has Page Data: {page ? 'Yes' : 'No'}</p>
            <p>Checkout State: {checkoutState.status}</p>
          </div>

          <Card className="shadow-md">
            <CardContent className="p-6 md:p-8">
              {renderContent()}
            </CardContent>
          </Card>
        </div>
      </main>

      <footer className="bg-white border-t py-6">
        <div className="container mx-auto px-4 text-center text-sm text-muted-foreground">
          <p>© {new Date().getFullYear()} PayPal Invoicer. All rights reserved.</p>
          <p className="mt-1">Secure payment processing provided by PayPal.</p>
        </div>
      </footer>
    </div>
  );
}
